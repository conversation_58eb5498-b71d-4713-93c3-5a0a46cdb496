"""
Comprehensive test suite for the enhanced AI Companion System.
Tests WhatsApp integration, mental health platform, and microservices architecture.
"""

import pytest
import asyncio
import json
import time
from datetime import datetime, timezone
from unittest.mock import Mock, patch, AsyncMock
import requests

# Import system components
from conversation_service import ConversationService
from whatsapp_service import WhatsAppService, WhatsAppMessage, WhatsAppUser
from mental_health_platform import MentalHealthDataPlatform, AnonymizedProfile, ConsentType
from microservices_config import MicroservicesArchitecture, DeploymentPlatform
from models import EmotionType, InteractionType, PersonalMemory
from config import settings

class TestEnhancedAICompanion:
    """Test suite for enhanced AI Companion System."""
    
    @pytest.fixture
    def conversation_service(self):
        """Create conversation service for testing."""
        return ConversationService()
    
    @pytest.fixture
    def whatsapp_service(self):
        """Create WhatsApp service for testing."""
        return WhatsAppService()
    
    @pytest.fixture
    def mental_health_platform(self):
        """Create mental health platform for testing."""
        return MentalHealthDataPlatform()
    
    @pytest.fixture
    def microservices_architecture(self):
        """Create microservices architecture for testing."""
        return MicroservicesArchitecture()

    def test_conversation_service_basic_functionality(self, conversation_service):
        """Test basic conversation service functionality."""
        user_id = "test_user_001"
        message = "Hello, I'm feeling a bit anxious today."
        conversation_id = "test_conv_001"
        
        # Process message
        result = conversation_service.process_message(user_id, message, conversation_id)
        
        # Verify response
        assert result is not None
        assert 'response' in result
        assert 'emotion' in result
        assert len(result['response']) > 0
        
        # Verify emotional intelligence
        assert result['emotion'] in [e for e in EmotionType]
        
        print(f"✅ Conversation service test passed")
        print(f"   Response: {result['response'][:100]}...")
        print(f"   Detected emotion: {result['emotion']}")

    def test_whatsapp_message_parsing(self, whatsapp_service):
        """Test WhatsApp message parsing."""
        # Mock WhatsApp message data
        message_data = {
            "id": "wamid.test123",
            "from": "1234567890",
            "timestamp": "1640995200",
            "type": "text",
            "text": {
                "body": "Hello, I need someone to talk to."
            }
        }
        
        # Parse message
        whatsapp_message = whatsapp_service._parse_message(
            message_data, 
            "1234567890", 
            datetime.now(timezone.utc)
        )
        
        # Verify parsing
        assert whatsapp_message.message_id == "wamid.test123"
        assert whatsapp_message.from_number == "1234567890"
        assert whatsapp_message.content == "Hello, I need someone to talk to."
        assert whatsapp_message.message_type == "text"
        
        print(f"✅ WhatsApp message parsing test passed")

    def test_whatsapp_user_creation(self, whatsapp_service):
        """Test WhatsApp user creation and management."""
        phone_number = "1234567890"
        contacts = [
            {
                "wa_id": "1234567890",
                "profile": {
                    "name": "Test User"
                }
            }
        ]
        
        # Create user
        user = whatsapp_service._get_or_create_user(phone_number, contacts)
        
        # Verify user creation
        assert user.phone_number == phone_number
        assert user.name == "Test User"
        assert user.user_id.startswith("whatsapp_")
        assert not user.consent_given  # Should start as False
        
        print(f"✅ WhatsApp user creation test passed")
        print(f"   User ID: {user.user_id}")
        print(f"   Name: {user.name}")

    def test_consent_processing(self, whatsapp_service):
        """Test consent processing for WhatsApp users."""
        # Test consent detection
        consent_messages = ["yes", "I agree", "I consent", "ok sure", "accept"]
        non_consent_messages = ["no", "I disagree", "maybe later", "not now"]
        
        for message in consent_messages:
            assert whatsapp_service._is_consent_message(message)
        
        for message in non_consent_messages:
            assert not whatsapp_service._is_consent_message(message)
        
        print(f"✅ Consent processing test passed")

    def test_rate_limiting(self, whatsapp_service):
        """Test rate limiting functionality."""
        phone_number = "test_rate_limit"
        
        # Test within rate limit
        for i in range(5):
            assert whatsapp_service._check_rate_limit(phone_number)
        
        # Test exceeding rate limit
        for i in range(10):
            whatsapp_service._check_rate_limit(phone_number)
        
        # Should be rate limited now
        assert not whatsapp_service._check_rate_limit(phone_number)
        
        print(f"✅ Rate limiting test passed")

    def test_mental_health_anonymization(self, mental_health_platform):
        """Test mental health data anonymization."""
        user_id = "test_user_mental_health"
        
        # Grant consent
        mental_health_platform.update_user_consent(
            user_id, 
            ConsentType.ANONYMIZED_SHARING, 
            True
        )
        
        # Create test memories
        memories = [
            PersonalMemory(
                id="mem1",
                user_id=user_id,
                content="I've been feeling really anxious lately",
                emotion=EmotionType.ANXIETY,
                interaction_type=InteractionType.CONVERSATION,
                importance=0.8
            ),
            PersonalMemory(
                id="mem2",
                user_id=user_id,
                content="Work has been overwhelming",
                emotion=EmotionType.SADNESS,
                interaction_type=InteractionType.CONVERSATION,
                importance=0.7
            )
        ]
        
        # Anonymize data
        profile = mental_health_platform.anonymize_user_data(user_id, memories)
        
        # Verify anonymization
        assert profile is not None
        assert profile.profile_id.startswith("anon_")
        assert profile.profile_id != user_id
        assert EmotionType.ANXIETY in profile.emotional_patterns
        assert "anxiety_support" in profile.support_needs
        
        print(f"✅ Mental health anonymization test passed")
        print(f"   Anonymous ID: {profile.profile_id}")
        print(f"   Support needs: {profile.support_needs}")

    def test_mental_health_insights_generation(self, mental_health_platform):
        """Test mental health insights generation."""
        # Create test anonymized profiles
        profiles = []
        for i in range(15):  # Above minimum cohort size
            profile = AnonymizedProfile(
                profile_id=f"anon_{i}",
                age_range="25-35",
                gender=None,
                location_region="North America",
                emotional_patterns={
                    EmotionType.ANXIETY: 0.4,
                    EmotionType.SADNESS: 0.3,
                    EmotionType.JOY: 0.3
                },
                interaction_frequency=2.5,
                support_needs=["anxiety_support", "work_stress"],
                risk_indicators=["social_isolation"],
                created_at=datetime.now(timezone.utc),
                last_updated=datetime.now(timezone.utc)
            )
            profiles.append(profile)
        
        # Generate insights
        insights = mental_health_platform.generate_population_insights(profiles)
        
        # Verify insights
        assert len(insights) > 0
        assert any(insight.insight_type == "emotional_trend" for insight in insights)
        assert any(insight.insight_type == "risk_analysis" for insight in insights)
        
        print(f"✅ Mental health insights generation test passed")
        print(f"   Generated {len(insights)} insights")

    def test_microservices_configuration(self, microservices_architecture):
        """Test microservices configuration."""
        # Test service definitions
        services = microservices_architecture.services
        
        # Verify core services exist
        required_services = [
            "conversation-service",
            "memory-service",
            "whatsapp-service",
            "mental-health-platform"
        ]
        
        for service_name in required_services:
            assert service_name in services
            service = services[service_name]
            assert service.port > 0
            assert service.cpu_limit
            assert service.memory_limit
        
        print(f"✅ Microservices configuration test passed")
        print(f"   Configured {len(services)} services")

    def test_deployment_order(self, microservices_architecture):
        """Test deployment order calculation."""
        deployment_order = microservices_architecture.get_deployment_order()
        
        # Verify dependencies are respected
        service_positions = {service: i for i, service in enumerate(deployment_order)}
        
        for service_name, config in microservices_architecture.services.items():
            service_pos = service_positions[service_name]
            for dependency in config.dependencies:
                if dependency in service_positions:
                    dep_pos = service_positions[dependency]
                    assert dep_pos < service_pos, f"{dependency} should be deployed before {service_name}"
        
        print(f"✅ Deployment order test passed")
        print(f"   Deployment order: {deployment_order}")

    def test_docker_compose_generation(self, microservices_architecture):
        """Test Docker Compose configuration generation."""
        compose_content = microservices_architecture.generate_docker_compose()
        
        # Verify compose file structure
        assert "version:" in compose_content
        assert "services:" in compose_content
        assert "networks:" in compose_content
        assert "redis:" in compose_content
        assert "postgres:" in compose_content
        
        print(f"✅ Docker Compose generation test passed")

    def test_render_config_generation(self, microservices_architecture):
        """Test Render.com configuration generation."""
        service_name = "conversation-service"
        render_config = microservices_architecture.generate_render_config(service_name)
        
        # Verify Render configuration
        assert "services" in render_config
        assert len(render_config["services"]) == 1
        
        service_config = render_config["services"][0]
        assert service_config["name"] == service_name
        assert service_config["type"] == "web"
        assert service_config["env"] == "python"
        
        print(f"✅ Render configuration generation test passed")

    @pytest.mark.asyncio
    async def test_whatsapp_conversation_flow(self, whatsapp_service):
        """Test complete WhatsApp conversation flow."""
        # Mock conversation service
        with patch.object(whatsapp_service, 'conversation_service') as mock_conv_service:
            mock_conv_service.process_message.return_value = {
                'response': 'I understand you\'re feeling anxious. I\'m here to help.',
                'emotion': EmotionType.EMPATHY
            }
            
            # Create test user
            user = WhatsAppUser(
                phone_number="1234567890",
                name="Test User",
                profile_name="Test User",
                user_id="whatsapp_test123",
                consent_given=True
            )
            
            # Create test message
            message = WhatsAppMessage(
                message_id="msg123",
                from_number="1234567890",
                to_number="whatsapp_bot",
                message_type="text",
                content="I'm feeling really anxious today",
                timestamp=datetime.now(timezone.utc)
            )
            
            # Mock send message
            with patch.object(whatsapp_service, '_send_message', new_callable=AsyncMock) as mock_send:
                # Process conversation message
                await whatsapp_service._process_conversation_message(user, message)
                
                # Verify message was sent
                mock_send.assert_called_once()
                call_args = mock_send.call_args[0]
                assert call_args[0] == user.phone_number
                assert "anxious" in call_args[1] or "help" in call_args[1]
        
        print(f"✅ WhatsApp conversation flow test passed")

    def test_emotion_emoji_mapping(self, whatsapp_service):
        """Test emotion to emoji mapping."""
        test_emotions = [
            (EmotionType.JOY, "😊"),
            (EmotionType.SADNESS, "😔"),
            (EmotionType.ANXIETY, "😟"),
            (EmotionType.LOVE, "❤️")
        ]
        
        for emotion, expected_emoji in test_emotions:
            emoji = whatsapp_service._get_emotion_emoji(emotion)
            assert emoji == expected_emoji
        
        print(f"✅ Emotion emoji mapping test passed")

    def test_system_integration(self, conversation_service, mental_health_platform):
        """Test integration between conversation service and mental health platform."""
        user_id = "integration_test_user"
        
        # Grant consent for mental health data
        mental_health_platform.update_user_consent(
            user_id,
            ConsentType.ANONYMIZED_SHARING,
            True
        )
        
        # Have a conversation
        messages = [
            "I've been feeling really down lately",
            "Work is stressing me out",
            "I can't sleep at night",
            "I feel like nobody understands me"
        ]
        
        conversation_id = "integration_test_conv"
        
        for message in messages:
            result = conversation_service.process_message(user_id, message, conversation_id)
            assert result is not None
            assert 'response' in result
        
        # Get user memories for mental health analysis
        memories = conversation_service.memory_service.get_personal_memories(user_id, limit=10)
        
        if memories:
            # Anonymize for mental health platform
            profile = mental_health_platform.anonymize_user_data(user_id, memories)
            
            if profile:
                assert profile.profile_id != user_id
                assert len(profile.support_needs) > 0
                print(f"✅ System integration test passed")
                print(f"   Identified support needs: {profile.support_needs}")
            else:
                print(f"⚠️  Mental health anonymization skipped (no consent or insufficient data)")
        else:
            print(f"⚠️  No memories found for integration test")

def run_comprehensive_test():
    """Run comprehensive test suite."""
    print("🧪 Starting Enhanced AI Companion System Tests")
    print("=" * 60)
    
    # Run pytest
    pytest.main([
        __file__,
        "-v",
        "--tb=short",
        "-x"  # Stop on first failure
    ])
    
    print("=" * 60)
    print("🎉 Enhanced AI Companion System Tests Complete!")

if __name__ == "__main__":
    run_comprehensive_test()
