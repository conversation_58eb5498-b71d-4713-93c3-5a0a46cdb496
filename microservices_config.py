"""
Microservices Configuration for AI Companion System.
Optimized for deployment on free-tier GPU platforms like Render.com.
"""

import os
import json
import logging
from typing import Dict, List, Optional, Any
from dataclasses import dataclass, field
from enum import Enum
import yaml

logger = logging.getLogger(__name__)

class ServiceType(Enum):
    """Types of microservices."""
    CORE_AI = "core_ai"
    INTERFACE = "interface"
    DATA = "data"
    MONITORING = "monitoring"
    INTEGRATION = "integration"

class DeploymentPlatform(Enum):
    """Supported deployment platforms."""
    RENDER = "render"
    RAILWAY = "railway"
    HEROKU = "heroku"
    DOCKER_LOCAL = "docker_local"

@dataclass
class ServiceConfig:
    """Configuration for a microservice."""
    name: str
    service_type: ServiceType
    port: int
    cpu_limit: str = "0.5"
    memory_limit: str = "512Mi"
    replicas: int = 1
    health_check_path: str = "/health"
    dependencies: List[str] = field(default_factory=list)
    environment_vars: Dict[str, str] = field(default_factory=dict)
    gpu_required: bool = False
    auto_scaling: bool = True
    min_replicas: int = 1
    max_replicas: int = 3

@dataclass
class PlatformConfig:
    """Platform-specific deployment configuration."""
    platform: DeploymentPlatform
    base_url: str
    docker_registry: Optional[str] = None
    resource_limits: Dict[str, str] = field(default_factory=dict)
    networking: Dict[str, Any] = field(default_factory=dict)
    secrets: List[str] = field(default_factory=list)

class MicroservicesArchitecture:
    """Microservices architecture configuration and management."""
    
    def __init__(self):
        """Initialize microservices architecture."""
        self.services = self._define_services()
        self.platforms = self._define_platforms()
        self.service_mesh = self._define_service_mesh()
        
    def _define_services(self) -> Dict[str, ServiceConfig]:
        """Define all microservices in the architecture."""
        return {
            # Core AI Services
            "conversation-service": ServiceConfig(
                name="conversation-service",
                service_type=ServiceType.CORE_AI,
                port=8001,
                cpu_limit="1.0",
                memory_limit="1Gi",
                gpu_required=True,
                dependencies=["memory-service", "gemini-service", "emotional-intelligence"],
                environment_vars={
                    "SERVICE_NAME": "conversation-service",
                    "LOG_LEVEL": "INFO",
                    "GEMINI_API_KEY": "${GEMINI_API_KEY}",
                    "REDIS_URL": "${REDIS_URL}"
                }
            ),
            
            "memory-service": ServiceConfig(
                name="memory-service",
                service_type=ServiceType.DATA,
                port=8002,
                cpu_limit="0.5",
                memory_limit="512Mi",
                dependencies=["storage-service"],
                environment_vars={
                    "SERVICE_NAME": "memory-service",
                    "REDIS_URL": "${REDIS_URL}",
                    "DATABASE_URL": "${DATABASE_URL}"
                }
            ),
            
            "emotional-intelligence": ServiceConfig(
                name="emotional-intelligence",
                service_type=ServiceType.CORE_AI,
                port=8003,
                cpu_limit="0.8",
                memory_limit="768Mi",
                gpu_required=True,
                dependencies=["gemini-service"],
                environment_vars={
                    "SERVICE_NAME": "emotional-intelligence",
                    "GEMINI_API_KEY": "${GEMINI_API_KEY}"
                }
            ),
            
            "learning-service": ServiceConfig(
                name="learning-service",
                service_type=ServiceType.CORE_AI,
                port=8004,
                cpu_limit="0.6",
                memory_limit="512Mi",
                dependencies=["memory-service", "gemini-service"],
                environment_vars={
                    "SERVICE_NAME": "learning-service",
                    "GEMINI_API_KEY": "${GEMINI_API_KEY}",
                    "REDIS_URL": "${REDIS_URL}"
                }
            ),
            
            "gemini-service": ServiceConfig(
                name="gemini-service",
                service_type=ServiceType.INTEGRATION,
                port=8005,
                cpu_limit="0.4",
                memory_limit="256Mi",
                environment_vars={
                    "SERVICE_NAME": "gemini-service",
                    "GEMINI_API_KEY": "${GEMINI_API_KEY}"
                }
            ),
            
            # Interface Services
            "whatsapp-service": ServiceConfig(
                name="whatsapp-service",
                service_type=ServiceType.INTERFACE,
                port=8006,
                cpu_limit="0.5",
                memory_limit="512Mi",
                dependencies=["conversation-service"],
                environment_vars={
                    "SERVICE_NAME": "whatsapp-service",
                    "WHATSAPP_ACCESS_TOKEN": "${WHATSAPP_ACCESS_TOKEN}",
                    "WHATSAPP_PHONE_NUMBER_ID": "${WHATSAPP_PHONE_NUMBER_ID}",
                    "WHATSAPP_VERIFY_TOKEN": "${WHATSAPP_VERIFY_TOKEN}",
                    "WHATSAPP_APP_SECRET": "${WHATSAPP_APP_SECRET}",
                    "CONVERSATION_SERVICE_URL": "http://conversation-service:8001"
                }
            ),
            
            "gradio-interface": ServiceConfig(
                name="gradio-interface",
                service_type=ServiceType.INTERFACE,
                port=7860,
                cpu_limit="0.3",
                memory_limit="256Mi",
                dependencies=["conversation-service"],
                environment_vars={
                    "SERVICE_NAME": "gradio-interface",
                    "CONVERSATION_SERVICE_URL": "http://conversation-service:8001"
                }
            ),
            
            "api-gateway": ServiceConfig(
                name="api-gateway",
                service_type=ServiceType.INTERFACE,
                port=8000,
                cpu_limit="0.4",
                memory_limit="256Mi",
                dependencies=["conversation-service", "mental-health-platform"],
                environment_vars={
                    "SERVICE_NAME": "api-gateway",
                    "CONVERSATION_SERVICE_URL": "http://conversation-service:8001",
                    "MENTAL_HEALTH_SERVICE_URL": "http://mental-health-platform:8008"
                }
            ),
            
            # Data Services
            "storage-service": ServiceConfig(
                name="storage-service",
                service_type=ServiceType.DATA,
                port=8007,
                cpu_limit="0.3",
                memory_limit="256Mi",
                environment_vars={
                    "SERVICE_NAME": "storage-service",
                    "DATABASE_URL": "${DATABASE_URL}",
                    "REDIS_URL": "${REDIS_URL}"
                }
            ),
            
            "mental-health-platform": ServiceConfig(
                name="mental-health-platform",
                service_type=ServiceType.DATA,
                port=8008,
                cpu_limit="0.6",
                memory_limit="512Mi",
                dependencies=["storage-service"],
                environment_vars={
                    "SERVICE_NAME": "mental-health-platform",
                    "DATABASE_URL": "${DATABASE_URL}",
                    "SECRET_KEY": "${SECRET_KEY}"
                }
            ),
            
            # Monitoring Services
            "monitoring-service": ServiceConfig(
                name="monitoring-service",
                service_type=ServiceType.MONITORING,
                port=8009,
                cpu_limit="0.2",
                memory_limit="128Mi",
                environment_vars={
                    "SERVICE_NAME": "monitoring-service",
                    "REDIS_URL": "${REDIS_URL}"
                }
            )
        }
    
    def _define_platforms(self) -> Dict[DeploymentPlatform, PlatformConfig]:
        """Define platform-specific configurations."""
        return {
            DeploymentPlatform.RENDER: PlatformConfig(
                platform=DeploymentPlatform.RENDER,
                base_url="https://api.render.com",
                resource_limits={
                    "free_tier_cpu": "0.1",
                    "free_tier_memory": "512Mi",
                    "paid_tier_cpu": "2.0",
                    "paid_tier_memory": "2Gi"
                },
                networking={
                    "internal_communication": "service-discovery",
                    "load_balancer": "automatic",
                    "ssl": "automatic"
                },
                secrets=[
                    "GEMINI_API_KEY",
                    "WHATSAPP_ACCESS_TOKEN",
                    "WHATSAPP_PHONE_NUMBER_ID",
                    "WHATSAPP_VERIFY_TOKEN",
                    "WHATSAPP_APP_SECRET",
                    "SECRET_KEY",
                    "DATABASE_URL",
                    "REDIS_URL"
                ]
            ),
            
            DeploymentPlatform.RAILWAY: PlatformConfig(
                platform=DeploymentPlatform.RAILWAY,
                base_url="https://railway.app",
                resource_limits={
                    "free_tier_cpu": "0.5",
                    "free_tier_memory": "512Mi",
                    "paid_tier_cpu": "8.0",
                    "paid_tier_memory": "8Gi"
                },
                networking={
                    "internal_communication": "private-network",
                    "load_balancer": "automatic",
                    "ssl": "automatic"
                }
            ),
            
            DeploymentPlatform.DOCKER_LOCAL: PlatformConfig(
                platform=DeploymentPlatform.DOCKER_LOCAL,
                base_url="http://localhost",
                docker_registry="local",
                networking={
                    "internal_communication": "docker-network",
                    "load_balancer": "nginx",
                    "ssl": "self-signed"
                }
            )
        }
    
    def _define_service_mesh(self) -> Dict[str, Any]:
        """Define service mesh configuration."""
        return {
            "discovery": {
                "type": "dns",
                "namespace": "ai-companion",
                "domain": "cluster.local"
            },
            "communication": {
                "protocol": "http",
                "timeout": "30s",
                "retries": 3,
                "circuit_breaker": True
            },
            "security": {
                "tls": True,
                "authentication": "jwt",
                "authorization": "rbac"
            },
            "observability": {
                "tracing": True,
                "metrics": True,
                "logging": "structured"
            }
        }
    
    def generate_docker_compose(self, platform: DeploymentPlatform = DeploymentPlatform.DOCKER_LOCAL) -> str:
        """Generate docker-compose.yml for local development."""
        compose_config = {
            "version": "3.8",
            "services": {},
            "networks": {
                "ai-companion-network": {
                    "driver": "bridge"
                }
            },
            "volumes": {
                "redis-data": {},
                "postgres-data": {}
            }
        }
        
        # Add services
        for service_name, config in self.services.items():
            service_config = {
                "build": {
                    "context": ".",
                    "dockerfile": f"docker/Dockerfile.{service_name}"
                },
                "ports": [f"{config.port}:{config.port}"],
                "environment": config.environment_vars,
                "networks": ["ai-companion-network"],
                "depends_on": config.dependencies,
                "restart": "unless-stopped",
                "healthcheck": {
                    "test": f"curl -f http://localhost:{config.port}{config.health_check_path} || exit 1",
                    "interval": "30s",
                    "timeout": "10s",
                    "retries": 3
                }
            }
            
            # Add resource limits
            if platform == DeploymentPlatform.DOCKER_LOCAL:
                service_config["deploy"] = {
                    "resources": {
                        "limits": {
                            "cpus": config.cpu_limit,
                            "memory": config.memory_limit
                        }
                    }
                }
            
            compose_config["services"][service_name] = service_config
        
        # Add infrastructure services
        compose_config["services"]["redis"] = {
            "image": "redis:7-alpine",
            "ports": ["6379:6379"],
            "volumes": ["redis-data:/data"],
            "networks": ["ai-companion-network"],
            "restart": "unless-stopped"
        }
        
        compose_config["services"]["postgres"] = {
            "image": "postgres:15-alpine",
            "environment": {
                "POSTGRES_DB": "ai_companion",
                "POSTGRES_USER": "ai_companion",
                "POSTGRES_PASSWORD": "ai_companion_password"
            },
            "ports": ["5432:5432"],
            "volumes": ["postgres-data:/var/lib/postgresql/data"],
            "networks": ["ai-companion-network"],
            "restart": "unless-stopped"
        }
        
        return yaml.dump(compose_config, default_flow_style=False)
    
    def generate_render_config(self, service_name: str) -> Dict[str, Any]:
        """Generate Render.com configuration for a service."""
        if service_name not in self.services:
            raise ValueError(f"Service {service_name} not found")
        
        config = self.services[service_name]
        platform_config = self.platforms[DeploymentPlatform.RENDER]
        
        render_config = {
            "services": [
                {
                    "type": "web",
                    "name": service_name,
                    "env": "python",
                    "buildCommand": "pip install -r requirements.txt",
                    "startCommand": f"python -m {service_name.replace('-', '_')}",
                    "plan": "free" if not config.gpu_required else "starter",
                    "envVars": [
                        {"key": key, "value": value}
                        for key, value in config.environment_vars.items()
                    ],
                    "healthCheckPath": config.health_check_path,
                    "autoDeploy": True
                }
            ]
        }
        
        return render_config
    
    def generate_kubernetes_manifests(self) -> Dict[str, str]:
        """Generate Kubernetes manifests for all services."""
        manifests = {}
        
        for service_name, config in self.services.items():
            manifest = self._generate_k8s_deployment(service_name, config)
            manifests[f"{service_name}-deployment.yaml"] = yaml.dump(manifest)
            
            service_manifest = self._generate_k8s_service(service_name, config)
            manifests[f"{service_name}-service.yaml"] = yaml.dump(service_manifest)
        
        return manifests
    
    def _generate_k8s_deployment(self, service_name: str, config: ServiceConfig) -> Dict[str, Any]:
        """Generate Kubernetes deployment manifest."""
        return {
            "apiVersion": "apps/v1",
            "kind": "Deployment",
            "metadata": {
                "name": service_name,
                "labels": {
                    "app": service_name,
                    "service-type": config.service_type.value
                }
            },
            "spec": {
                "replicas": config.replicas,
                "selector": {
                    "matchLabels": {
                        "app": service_name
                    }
                },
                "template": {
                    "metadata": {
                        "labels": {
                            "app": service_name
                        }
                    },
                    "spec": {
                        "containers": [
                            {
                                "name": service_name,
                                "image": f"ai-companion/{service_name}:latest",
                                "ports": [
                                    {
                                        "containerPort": config.port
                                    }
                                ],
                                "env": [
                                    {"name": key, "value": value}
                                    for key, value in config.environment_vars.items()
                                ],
                                "resources": {
                                    "limits": {
                                        "cpu": config.cpu_limit,
                                        "memory": config.memory_limit
                                    },
                                    "requests": {
                                        "cpu": str(float(config.cpu_limit) * 0.5),
                                        "memory": str(int(config.memory_limit.rstrip('Mi')) // 2) + "Mi"
                                    }
                                },
                                "livenessProbe": {
                                    "httpGet": {
                                        "path": config.health_check_path,
                                        "port": config.port
                                    },
                                    "initialDelaySeconds": 30,
                                    "periodSeconds": 10
                                },
                                "readinessProbe": {
                                    "httpGet": {
                                        "path": config.health_check_path,
                                        "port": config.port
                                    },
                                    "initialDelaySeconds": 5,
                                    "periodSeconds": 5
                                }
                            }
                        ]
                    }
                }
            }
        }
    
    def _generate_k8s_service(self, service_name: str, config: ServiceConfig) -> Dict[str, Any]:
        """Generate Kubernetes service manifest."""
        return {
            "apiVersion": "v1",
            "kind": "Service",
            "metadata": {
                "name": service_name,
                "labels": {
                    "app": service_name
                }
            },
            "spec": {
                "selector": {
                    "app": service_name
                },
                "ports": [
                    {
                        "port": config.port,
                        "targetPort": config.port,
                        "protocol": "TCP"
                    }
                ],
                "type": "ClusterIP"
            }
        }
    
    def get_service_dependencies(self, service_name: str) -> List[str]:
        """Get dependencies for a service."""
        if service_name not in self.services:
            return []
        return self.services[service_name].dependencies
    
    def get_deployment_order(self) -> List[str]:
        """Get optimal deployment order based on dependencies."""
        deployed = set()
        order = []
        
        def can_deploy(service_name: str) -> bool:
            deps = self.get_service_dependencies(service_name)
            return all(dep in deployed for dep in deps)
        
        while len(deployed) < len(self.services):
            for service_name in self.services:
                if service_name not in deployed and can_deploy(service_name):
                    order.append(service_name)
                    deployed.add(service_name)
                    break
        
        return order
