"""
Deployment script for AI Companion System microservices.
Supports multiple platforms including Render.com, Railway, and local Docker.
"""

import os
import sys
import json
import yaml
import logging
import argparse
import subprocess
import time
from pathlib import Path
from typing import Dict, List, Optional, Any
import requests

from microservices_config import MicroservicesArchitecture, DeploymentPlatform, ServiceType
from config import settings

logger = logging.getLogger(__name__)

class DeploymentManager:
    """Manages deployment of AI Companion microservices."""
    
    def __init__(self):
        """Initialize deployment manager."""
        self.architecture = MicroservicesArchitecture()
        self.deployment_status: Dict[str, str] = {}
        
    def deploy_to_render(self, services: Optional[List[str]] = None) -> bool:
        """Deploy services to Render.com."""
        try:
            logger.info("Starting deployment to Render.com...")
            
            services_to_deploy = services or list(self.architecture.services.keys())
            deployment_order = self._get_filtered_deployment_order(services_to_deploy)
            
            for service_name in deployment_order:
                logger.info(f"Deploying {service_name} to Render...")
                
                # Generate Render configuration
                render_config = self.architecture.generate_render_config(service_name)
                
                # Create render.yaml file
                render_file = Path(f"render-{service_name}.yaml")
                with open(render_file, 'w') as f:
                    yaml.dump(render_config, f)
                
                # Deploy using Render CLI or API
                success = self._deploy_service_to_render(service_name, render_config)
                
                if success:
                    self.deployment_status[service_name] = "deployed"
                    logger.info(f"✅ {service_name} deployed successfully")
                else:
                    self.deployment_status[service_name] = "failed"
                    logger.error(f"❌ {service_name} deployment failed")
                    return False
                
                # Wait between deployments to avoid rate limits
                time.sleep(10)
            
            logger.info("🎉 All services deployed to Render successfully!")
            return True
            
        except Exception as e:
            logger.error(f"Deployment to Render failed: {e}")
            return False
    
    def deploy_to_docker(self, services: Optional[List[str]] = None) -> bool:
        """Deploy services using Docker Compose."""
        try:
            logger.info("Starting Docker deployment...")
            
            # Generate docker-compose.yml
            compose_content = self.architecture.generate_docker_compose()
            
            with open("docker-compose.yml", "w") as f:
                f.write(compose_content)
            
            # Create Dockerfiles for each service
            self._create_dockerfiles()
            
            # Build and start services
            services_arg = " ".join(services) if services else ""
            
            logger.info("Building Docker images...")
            result = subprocess.run(
                f"docker-compose build {services_arg}",
                shell=True,
                capture_output=True,
                text=True
            )
            
            if result.returncode != 0:
                logger.error(f"Docker build failed: {result.stderr}")
                return False
            
            logger.info("Starting services...")
            result = subprocess.run(
                f"docker-compose up -d {services_arg}",
                shell=True,
                capture_output=True,
                text=True
            )
            
            if result.returncode != 0:
                logger.error(f"Docker start failed: {result.stderr}")
                return False
            
            # Wait for services to be healthy
            self._wait_for_services_health()
            
            logger.info("🎉 Docker deployment completed successfully!")
            return True
            
        except Exception as e:
            logger.error(f"Docker deployment failed: {e}")
            return False
    
    def deploy_to_kubernetes(self, namespace: str = "ai-companion") -> bool:
        """Deploy services to Kubernetes."""
        try:
            logger.info("Starting Kubernetes deployment...")
            
            # Create namespace
            subprocess.run(
                f"kubectl create namespace {namespace} --dry-run=client -o yaml | kubectl apply -f -",
                shell=True
            )
            
            # Generate and apply manifests
            manifests = self.architecture.generate_kubernetes_manifests()
            
            for filename, content in manifests.items():
                manifest_path = Path(f"k8s/{filename}")
                manifest_path.parent.mkdir(exist_ok=True)
                
                with open(manifest_path, "w") as f:
                    f.write(content)
                
                # Apply manifest
                result = subprocess.run(
                    f"kubectl apply -f {manifest_path} -n {namespace}",
                    shell=True,
                    capture_output=True,
                    text=True
                )
                
                if result.returncode != 0:
                    logger.error(f"Failed to apply {filename}: {result.stderr}")
                    return False
            
            logger.info("🎉 Kubernetes deployment completed successfully!")
            return True
            
        except Exception as e:
            logger.error(f"Kubernetes deployment failed: {e}")
            return False
    
    def _deploy_service_to_render(self, service_name: str, config: Dict[str, Any]) -> bool:
        """Deploy a single service to Render."""
        try:
            # This would use Render API or CLI
            # For now, we'll create the configuration files
            
            # Create render.yaml for the service
            render_yaml = {
                "services": [
                    {
                        "type": "web",
                        "name": service_name,
                        "env": "python",
                        "buildCommand": "pip install -r requirements.txt",
                        "startCommand": f"python -m services.{service_name.replace('-', '_')}",
                        "plan": "free",
                        "envVars": [
                            {"key": "SERVICE_NAME", "value": service_name},
                            {"key": "PORT", "value": str(self.architecture.services[service_name].port)}
                        ]
                    }
                ]
            }
            
            # Save configuration
            config_path = Path(f"deploy/render/{service_name}.yaml")
            config_path.parent.mkdir(parents=True, exist_ok=True)
            
            with open(config_path, "w") as f:
                yaml.dump(render_yaml, f)
            
            logger.info(f"Render configuration created for {service_name}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to deploy {service_name} to Render: {e}")
            return False
    
    def _create_dockerfiles(self):
        """Create Dockerfiles for each service."""
        docker_dir = Path("docker")
        docker_dir.mkdir(exist_ok=True)
        
        # Base Dockerfile template
        base_dockerfile = """
FROM python:3.11-slim

WORKDIR /app

# Install system dependencies
RUN apt-get update && apt-get install -y \\
    curl \\
    && rm -rf /var/lib/apt/lists/*

# Copy requirements and install Python dependencies
COPY requirements.txt .
RUN pip install --no-cache-dir -r requirements.txt

# Copy application code
COPY . .

# Create non-root user
RUN useradd -m -u 1000 appuser && chown -R appuser:appuser /app
USER appuser

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \\
    CMD curl -f http://localhost:${PORT}/health || exit 1

# Expose port
EXPOSE ${PORT}

# Start command will be overridden by docker-compose
CMD ["python", "-m", "main"]
        """.strip()
        
        # Create service-specific Dockerfiles
        for service_name, config in self.architecture.services.items():
            dockerfile_path = docker_dir / f"Dockerfile.{service_name}"
            
            # Customize Dockerfile for service
            service_dockerfile = base_dockerfile.replace(
                'CMD ["python", "-m", "main"]',
                f'CMD ["python", "-m", "services.{service_name.replace("-", "_")}"]'
            )
            
            # Add GPU support if needed
            if config.gpu_required:
                service_dockerfile = service_dockerfile.replace(
                    "FROM python:3.11-slim",
                    "FROM nvidia/cuda:11.8-runtime-ubuntu20.04\n\n" +
                    "RUN apt-get update && apt-get install -y python3 python3-pip\n" +
                    "RUN ln -s /usr/bin/python3 /usr/bin/python"
                )
            
            with open(dockerfile_path, "w") as f:
                f.write(service_dockerfile)
    
    def _wait_for_services_health(self, timeout: int = 300):
        """Wait for all services to be healthy."""
        logger.info("Waiting for services to be healthy...")
        
        start_time = time.time()
        healthy_services = set()
        
        while time.time() - start_time < timeout:
            for service_name, config in self.architecture.services.items():
                if service_name in healthy_services:
                    continue
                
                try:
                    response = requests.get(
                        f"http://localhost:{config.port}/health",
                        timeout=5
                    )
                    
                    if response.status_code == 200:
                        healthy_services.add(service_name)
                        logger.info(f"✅ {service_name} is healthy")
                
                except requests.RequestException:
                    pass  # Service not ready yet
            
            if len(healthy_services) == len(self.architecture.services):
                logger.info("🎉 All services are healthy!")
                return True
            
            time.sleep(10)
        
        logger.warning(f"Timeout waiting for services. Healthy: {healthy_services}")
        return False
    
    def _get_filtered_deployment_order(self, services: List[str]) -> List[str]:
        """Get deployment order for filtered services."""
        full_order = self.architecture.get_deployment_order()
        return [service for service in full_order if service in services]
    
    def status(self) -> Dict[str, Any]:
        """Get deployment status."""
        return {
            "services": self.deployment_status,
            "total_services": len(self.architecture.services),
            "deployed_services": len([s for s in self.deployment_status.values() if s == "deployed"]),
            "failed_services": len([s for s in self.deployment_status.values() if s == "failed"])
        }
    
    def rollback(self, service_name: str) -> bool:
        """Rollback a service deployment."""
        try:
            logger.info(f"Rolling back {service_name}...")
            
            # Stop the service
            subprocess.run(
                f"docker-compose stop {service_name}",
                shell=True
            )
            
            # Remove the service
            subprocess.run(
                f"docker-compose rm -f {service_name}",
                shell=True
            )
            
            self.deployment_status[service_name] = "rolled_back"
            logger.info(f"✅ {service_name} rolled back successfully")
            return True
            
        except Exception as e:
            logger.error(f"Rollback failed for {service_name}: {e}")
            return False
    
    def scale_service(self, service_name: str, replicas: int) -> bool:
        """Scale a service to specified number of replicas."""
        try:
            logger.info(f"Scaling {service_name} to {replicas} replicas...")
            
            result = subprocess.run(
                f"docker-compose up -d --scale {service_name}={replicas}",
                shell=True,
                capture_output=True,
                text=True
            )
            
            if result.returncode == 0:
                logger.info(f"✅ {service_name} scaled to {replicas} replicas")
                return True
            else:
                logger.error(f"Scaling failed: {result.stderr}")
                return False
                
        except Exception as e:
            logger.error(f"Scaling failed for {service_name}: {e}")
            return False

def main():
    """Main deployment script."""
    parser = argparse.ArgumentParser(description="Deploy AI Companion microservices")
    parser.add_argument(
        "--platform",
        choices=["docker", "render", "kubernetes"],
        default="docker",
        help="Deployment platform"
    )
    parser.add_argument(
        "--services",
        nargs="*",
        help="Specific services to deploy (default: all)"
    )
    parser.add_argument(
        "--action",
        choices=["deploy", "status", "rollback", "scale"],
        default="deploy",
        help="Action to perform"
    )
    parser.add_argument(
        "--service",
        help="Service name for rollback/scale actions"
    )
    parser.add_argument(
        "--replicas",
        type=int,
        default=1,
        help="Number of replicas for scaling"
    )
    
    args = parser.parse_args()
    
    # Setup logging
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    
    deployment_manager = DeploymentManager()
    
    if args.action == "deploy":
        if args.platform == "docker":
            success = deployment_manager.deploy_to_docker(args.services)
        elif args.platform == "render":
            success = deployment_manager.deploy_to_render(args.services)
        elif args.platform == "kubernetes":
            success = deployment_manager.deploy_to_kubernetes()
        
        sys.exit(0 if success else 1)
    
    elif args.action == "status":
        status = deployment_manager.status()
        print(json.dumps(status, indent=2))
    
    elif args.action == "rollback":
        if not args.service:
            print("Error: --service required for rollback")
            sys.exit(1)
        
        success = deployment_manager.rollback(args.service)
        sys.exit(0 if success else 1)
    
    elif args.action == "scale":
        if not args.service:
            print("Error: --service required for scaling")
            sys.exit(1)
        
        success = deployment_manager.scale_service(args.service, args.replicas)
        sys.exit(0 if success else 1)

if __name__ == "__main__":
    main()
