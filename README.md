# AI Companion System - Dual Memory Architecture

A sophisticated conversational AI companion that learns and adapts to individual users while maintaining strict privacy boundaries through dual-memory architecture.

## 🧠 Core Features

- **Personalized Memory**: Strictly isolated per-user memory containing private data, preferences, and contextual understanding
- **Universal Memory**: Shared knowledge base for general learning and trending topics
- **Emotional Intelligence**: Context-aware responses that remember past interactions
- **Adaptive Learning**: Continuous improvement based on user preferences and feedback
- **Gemini Integration**: Real-time context analysis and conversation grounding
- **Microservice Architecture**: Scalable, independently deployable services

## 🏗️ Enhanced Microservices Architecture

```
┌─────────────────────────────────────────────────────────────────────────────┐
│                           🌐 Interface Layer                                │
├─────────────────┬─────────────────┬─────────────────┬─────────────────────┤
│   WhatsApp Bot  │   Web Interface │   REST API      │   Mental Health     │
│   (Production)  │   (Gradio Dev)  │   (External)    │   Dashboard         │
└─────────────────┴─────────────────┴─────────────────┴─────────────────────┘
                                │
┌─────────────────────────────────────────────────────────────────────────────┐
│                        🧠 Core AI Services                                  │
├─────────────────┬─────────────────┬─────────────────┬─────────────────────┤
│  Conversation   │   Emotional     │    Memory       │    Learning         │
│   Orchestrator  │  Intelligence   │   Management    │   & Adaptation      │
│                 │   & Empathy     │   (Dual-Arch)   │                     │
└─────────────────┴─────────────────┴─────────────────┴─────────────────────┘
                                │
┌─────────────────────────────────────────────────────────────────────────────┐
│                      🔧 Infrastructure Services                             │
├─────────────────┬─────────────────┬─────────────────┬─────────────────────┤
│   Gemini API    │   Storage &     │   Monitoring    │   Mental Health     │
│   Integration   │   Persistence   │   & Health      │   Data Platform     │
│                 │   (Redis+SQL)   │   Checks        │   (Anonymized)      │
└─────────────────┴─────────────────┴─────────────────┴─────────────────────┘
```

## 🚀 Quick Start

1. **Install Dependencies**:
   ```bash
   pip install -r requirements.txt
   ```

2. **Set Environment Variables**:
   ```bash
   cp .env.example .env
   # Edit .env with your API keys
   ```

3. **Run the System**:
   ```bash
   python main.py
   ```

4. **Access Interface**:
   Open http://localhost:7860 in your browser

## 🔧 Configuration

### Required API Keys
- `GEMINI_API_KEY`: Google Gemini API key
- `OPENAI_API_KEY`: OpenAI API key (optional, for fallback)
- `REDIS_URL`: Redis connection string

### Environment Variables
- `MEMORY_TTL`: Memory retention period (default: 30 days)
- `LEARNING_RATE`: Adaptation speed (default: 0.1)
- `MAX_CONTEXT_LENGTH`: Conversation context limit (default: 2000)

## 🧪 Development

### Running Tests
```bash
pytest tests/
```

### Code Formatting
```bash
black .
flake8 .
```

## 📊 Memory Architecture

### Personal Memory
- User preferences and habits
- Emotional patterns and triggers
- Private conversation history
- Personal goals and interests
- Strict isolation per user ID

### Universal Memory
- General knowledge and trends
- Non-sensitive shared insights
- Global conversation patterns
- Public topic interests
- Aggregated learning data

## 🔒 Privacy & Security

- Personal memory is encrypted and isolated per user
- Universal memory contains only non-sensitive, aggregated data
- No cross-user data leakage
- GDPR-compliant data handling
- Automatic data retention policies

## 🎯 Use Cases

- **Personal Assistant**: Remembering preferences and routines
- **Emotional Support**: Context-aware empathetic responses
- **Learning Companion**: Adaptive educational content
- **Social Companion**: Engaging conversations based on interests
- **Memory Aid**: Long-term relationship building

## 🔬 Research Foundation

This system incorporates insights from:
- Neural-symbolic reasoning
- Neuro-inspired computing
- Human memory and cognition models
- Conversational AI research
- Privacy-preserving machine learning 