"""
Mental Health Data Platform for AI Companion System.
Privacy-first platform for anonymizing emotional data and providing insights to mental health professionals.
"""

import json
import hashlib
import logging
import asyncio
from datetime import datetime, timezone, timedelta
from typing import Dict, List, Optional, Any, Tuple
from dataclasses import dataclass, field
from enum import Enum
import numpy as np
from collections import defaultdict, Counter
import uuid

from models import EmotionType, InteractionType, MemoryEntry, PersonalMemory
from storage_service import PersistentStorageService
from config import settings
from monitoring_service import monitoring, monitor_performance, AlertLevel

logger = logging.getLogger(__name__)

class RiskLevel(Enum):
    """Risk assessment levels."""
    LOW = "low"
    MODERATE = "moderate"
    HIGH = "high"
    CRITICAL = "critical"

class ConsentType(Enum):
    """Types of consent for data usage."""
    BASIC_ANALYTICS = "basic_analytics"
    RESEARCH_PARTICIPATION = "research_participation"
    ANONYMIZED_SHARING = "anonymized_sharing"
    CRISIS_INTERVENTION = "crisis_intervention"

@dataclass
class AnonymizedProfile:
    """Anonymized user profile for research."""
    profile_id: str  # Anonymous ID
    age_range: str  # e.g., "25-30"
    gender: Optional[str]
    location_region: Optional[str]  # Broad region, not specific location
    emotional_patterns: Dict[EmotionType, float]
    interaction_frequency: float
    support_needs: List[str]
    risk_indicators: List[str]
    created_at: datetime
    last_updated: datetime

@dataclass
class MentalHealthInsight:
    """Aggregated mental health insight."""
    insight_id: str
    insight_type: str  # trend, pattern, risk_factor, intervention_opportunity
    title: str
    description: str
    affected_population: Dict[str, Any]  # Demographics without PII
    confidence_score: float
    supporting_data: Dict[str, Any]
    recommendations: List[str]
    created_at: datetime
    severity: RiskLevel

@dataclass
class ResearchDataset:
    """Anonymized dataset for research."""
    dataset_id: str
    title: str
    description: str
    data_points: int
    anonymized_profiles: List[AnonymizedProfile]
    insights: List[MentalHealthInsight]
    ethical_approval: bool
    consent_verified: bool
    created_at: datetime
    access_restrictions: Dict[str, Any]

class MentalHealthDataPlatform:
    """Privacy-first mental health data platform."""
    
    def __init__(self):
        """Initialize the mental health data platform."""
        self.storage_service = PersistentStorageService()
        
        # Anonymization settings
        self.anonymization_salt = settings.secret_key.encode()
        self.min_cohort_size = 10  # Minimum group size for anonymization
        
        # Risk detection patterns
        self.crisis_keywords = [
            'suicide', 'kill myself', 'end it all', 'not worth living',
            'hurt myself', 'self harm', 'cutting', 'overdose'
        ]
        
        self.depression_indicators = [
            'hopeless', 'worthless', 'empty', 'numb', 'can\'t sleep',
            'no energy', 'lost interest', 'everything is pointless'
        ]
        
        self.anxiety_indicators = [
            'panic', 'can\'t breathe', 'heart racing', 'overwhelming',
            'constant worry', 'can\'t stop thinking', 'terrified'
        ]
        
        # Consent tracking
        self.user_consents: Dict[str, Dict[ConsentType, bool]] = {}
        
        logger.info("Mental Health Data Platform initialized")

    @monitor_performance
    def anonymize_user_data(self, user_id: str, memories: List[PersonalMemory]) -> Optional[AnonymizedProfile]:
        """Create anonymized profile from user data."""
        try:
            # Check consent
            if not self._has_consent(user_id, ConsentType.ANONYMIZED_SHARING):
                return None
            
            # Generate anonymous ID
            anonymous_id = self._generate_anonymous_id(user_id)
            
            # Extract emotional patterns
            emotional_patterns = self._extract_emotional_patterns(memories)
            
            # Calculate interaction frequency
            interaction_frequency = self._calculate_interaction_frequency(memories)
            
            # Identify support needs
            support_needs = self._identify_support_needs(memories)
            
            # Detect risk indicators
            risk_indicators = self._detect_risk_indicators(memories)
            
            # Get demographic info (anonymized)
            demographics = self._get_anonymized_demographics(user_id)
            
            profile = AnonymizedProfile(
                profile_id=anonymous_id,
                age_range=demographics.get('age_range'),
                gender=demographics.get('gender'),
                location_region=demographics.get('region'),
                emotional_patterns=emotional_patterns,
                interaction_frequency=interaction_frequency,
                support_needs=support_needs,
                risk_indicators=risk_indicators,
                created_at=datetime.now(timezone.utc),
                last_updated=datetime.now(timezone.utc)
            )
            
            monitoring.record_metric('profile_anonymized', 1.0)
            return profile
            
        except Exception as e:
            logger.error(f"Error anonymizing user data: {e}")
            monitoring.record_error(e, 'anonymization')
            return None

    def _generate_anonymous_id(self, user_id: str) -> str:
        """Generate anonymous ID that can't be traced back."""
        # Use HMAC with secret salt for irreversible anonymization
        hash_input = f"{user_id}:{datetime.now().strftime('%Y-%m')}"
        anonymous_hash = hashlib.pbkdf2_hmac(
            'sha256',
            hash_input.encode(),
            self.anonymization_salt,
            100000  # iterations
        )
        return f"anon_{anonymous_hash.hex()[:16]}"

    def _extract_emotional_patterns(self, memories: List[PersonalMemory]) -> Dict[EmotionType, float]:
        """Extract emotional patterns from memories."""
        emotion_counts = Counter()
        total_memories = len(memories)
        
        if total_memories == 0:
            return {}
        
        for memory in memories:
            if memory.emotion:
                emotion_counts[memory.emotion] += 1
        
        # Convert to percentages
        patterns = {}
        for emotion, count in emotion_counts.items():
            patterns[emotion] = count / total_memories
        
        return patterns

    def _calculate_interaction_frequency(self, memories: List[PersonalMemory]) -> float:
        """Calculate user interaction frequency."""
        if not memories:
            return 0.0
        
        # Calculate interactions per day over the last 30 days
        cutoff_date = datetime.now(timezone.utc) - timedelta(days=30)
        recent_memories = [m for m in memories if m.created_at >= cutoff_date]
        
        if not recent_memories:
            return 0.0
        
        days_span = (datetime.now(timezone.utc) - recent_memories[0].created_at).days
        if days_span == 0:
            days_span = 1
        
        return len(recent_memories) / days_span

    def _identify_support_needs(self, memories: List[PersonalMemory]) -> List[str]:
        """Identify support needs from conversation patterns."""
        support_needs = []
        
        # Analyze content for support indicators
        all_content = " ".join([m.content.lower() for m in memories])
        
        if any(keyword in all_content for keyword in self.depression_indicators):
            support_needs.append("depression_support")
        
        if any(keyword in all_content for keyword in self.anxiety_indicators):
            support_needs.append("anxiety_support")
        
        if "relationship" in all_content or "partner" in all_content:
            support_needs.append("relationship_support")
        
        if "work" in all_content or "job" in all_content or "career" in all_content:
            support_needs.append("career_support")
        
        if "family" in all_content or "parents" in all_content:
            support_needs.append("family_support")
        
        return support_needs

    def _detect_risk_indicators(self, memories: List[PersonalMemory]) -> List[str]:
        """Detect mental health risk indicators."""
        risk_indicators = []
        
        all_content = " ".join([m.content.lower() for m in memories])
        
        # Crisis indicators
        if any(keyword in all_content for keyword in self.crisis_keywords):
            risk_indicators.append("crisis_risk")
        
        # Isolation indicators
        if "alone" in all_content or "no one" in all_content or "isolated" in all_content:
            risk_indicators.append("social_isolation")
        
        # Sleep issues
        if "can't sleep" in all_content or "insomnia" in all_content or "nightmares" in all_content:
            risk_indicators.append("sleep_disturbance")
        
        # Substance use
        if "drinking" in all_content or "drugs" in all_content or "alcohol" in all_content:
            risk_indicators.append("substance_use")
        
        return risk_indicators

    def _get_anonymized_demographics(self, user_id: str) -> Dict[str, str]:
        """Get anonymized demographic information."""
        # This would typically come from user profile data
        # For now, return placeholder data
        return {
            'age_range': '25-35',  # Broad age ranges
            'gender': None,  # Only if explicitly provided
            'region': 'North America'  # Broad geographic regions
        }

    @monitor_performance
    def generate_population_insights(self, profiles: List[AnonymizedProfile]) -> List[MentalHealthInsight]:
        """Generate insights from anonymized population data."""
        insights = []
        
        try:
            # Ensure minimum cohort size for privacy
            if len(profiles) < self.min_cohort_size:
                logger.warning(f"Insufficient data for insights: {len(profiles)} profiles")
                return insights
            
            # Emotional trend analysis
            emotional_trends = self._analyze_emotional_trends(profiles)
            if emotional_trends:
                insights.append(emotional_trends)
            
            # Risk factor analysis
            risk_analysis = self._analyze_risk_factors(profiles)
            if risk_analysis:
                insights.append(risk_analysis)
            
            # Support need patterns
            support_patterns = self._analyze_support_patterns(profiles)
            if support_patterns:
                insights.append(support_patterns)
            
            # Intervention opportunities
            intervention_opportunities = self._identify_intervention_opportunities(profiles)
            insights.extend(intervention_opportunities)
            
            monitoring.record_metric('insights_generated', len(insights))
            return insights
            
        except Exception as e:
            logger.error(f"Error generating insights: {e}")
            monitoring.record_error(e, 'insight_generation')
            return []

    def _analyze_emotional_trends(self, profiles: List[AnonymizedProfile]) -> Optional[MentalHealthInsight]:
        """Analyze emotional trends across population."""
        try:
            # Aggregate emotional patterns
            emotion_totals = defaultdict(float)
            for profile in profiles:
                for emotion, frequency in profile.emotional_patterns.items():
                    emotion_totals[emotion] += frequency
            
            # Calculate averages
            total_profiles = len(profiles)
            emotion_averages = {
                emotion: total / total_profiles 
                for emotion, total in emotion_totals.items()
            }
            
            # Find dominant emotions
            dominant_emotions = sorted(
                emotion_averages.items(), 
                key=lambda x: x[1], 
                reverse=True
            )[:3]
            
            # Determine severity based on negative emotions
            negative_emotions = [EmotionType.SADNESS, EmotionType.ANGER, EmotionType.FEAR, EmotionType.ANXIETY]
            negative_score = sum(
                emotion_averages.get(emotion, 0) 
                for emotion in negative_emotions
            )
            
            severity = RiskLevel.LOW
            if negative_score > 0.4:
                severity = RiskLevel.HIGH
            elif negative_score > 0.25:
                severity = RiskLevel.MODERATE
            
            return MentalHealthInsight(
                insight_id=str(uuid.uuid4()),
                insight_type="emotional_trend",
                title="Population Emotional Trends",
                description=f"Analysis of emotional patterns across {total_profiles} anonymized profiles",
                affected_population={"total_profiles": total_profiles},
                confidence_score=0.85,
                supporting_data={
                    "dominant_emotions": [(e.value, f) for e, f in dominant_emotions],
                    "negative_emotion_score": negative_score
                },
                recommendations=[
                    "Increase availability of emotional support resources",
                    "Develop targeted interventions for dominant negative emotions",
                    "Monitor trends for early intervention opportunities"
                ],
                created_at=datetime.now(timezone.utc),
                severity=severity
            )
            
        except Exception as e:
            logger.error(f"Error analyzing emotional trends: {e}")
            return None

    def _analyze_risk_factors(self, profiles: List[AnonymizedProfile]) -> Optional[MentalHealthInsight]:
        """Analyze risk factors across population."""
        try:
            risk_counts = Counter()
            for profile in profiles:
                for risk in profile.risk_indicators:
                    risk_counts[risk] += 1
            
            total_profiles = len(profiles)
            high_risk_threshold = 0.15  # 15% of population
            
            high_risk_factors = [
                (risk, count) for risk, count in risk_counts.items()
                if count / total_profiles >= high_risk_threshold
            ]
            
            if not high_risk_factors:
                return None
            
            severity = RiskLevel.CRITICAL if any(
                risk == "crisis_risk" for risk, _ in high_risk_factors
            ) else RiskLevel.HIGH
            
            return MentalHealthInsight(
                insight_id=str(uuid.uuid4()),
                insight_type="risk_analysis",
                title="Population Risk Factor Analysis",
                description=f"Identified significant risk factors affecting {total_profiles} profiles",
                affected_population={"total_profiles": total_profiles},
                confidence_score=0.90,
                supporting_data={
                    "high_risk_factors": high_risk_factors,
                    "risk_distribution": dict(risk_counts)
                },
                recommendations=[
                    "Implement targeted screening for high-risk factors",
                    "Develop crisis intervention protocols",
                    "Increase mental health resource accessibility"
                ],
                created_at=datetime.now(timezone.utc),
                severity=severity
            )
            
        except Exception as e:
            logger.error(f"Error analyzing risk factors: {e}")
            return None

    def _analyze_support_patterns(self, profiles: List[AnonymizedProfile]) -> Optional[MentalHealthInsight]:
        """Analyze support need patterns."""
        try:
            support_counts = Counter()
            for profile in profiles:
                for need in profile.support_needs:
                    support_counts[need] += 1
            
            total_profiles = len(profiles)
            
            if not support_counts:
                return None
            
            top_needs = support_counts.most_common(5)
            
            return MentalHealthInsight(
                insight_id=str(uuid.uuid4()),
                insight_type="support_pattern",
                title="Support Need Patterns",
                description=f"Analysis of support needs across {total_profiles} profiles",
                affected_population={"total_profiles": total_profiles},
                confidence_score=0.80,
                supporting_data={
                    "top_support_needs": top_needs,
                    "support_distribution": dict(support_counts)
                },
                recommendations=[
                    "Allocate resources based on most common support needs",
                    "Develop specialized programs for top needs",
                    "Create peer support networks for common challenges"
                ],
                created_at=datetime.now(timezone.utc),
                severity=RiskLevel.MODERATE
            )
            
        except Exception as e:
            logger.error(f"Error analyzing support patterns: {e}")
            return None

    def _identify_intervention_opportunities(self, profiles: List[AnonymizedProfile]) -> List[MentalHealthInsight]:
        """Identify intervention opportunities."""
        opportunities = []
        
        try:
            # Early intervention for anxiety patterns
            anxiety_profiles = [
                p for p in profiles 
                if "anxiety_support" in p.support_needs
            ]
            
            if len(anxiety_profiles) >= self.min_cohort_size:
                opportunities.append(MentalHealthInsight(
                    insight_id=str(uuid.uuid4()),
                    insight_type="intervention_opportunity",
                    title="Early Anxiety Intervention Opportunity",
                    description=f"Identified {len(anxiety_profiles)} profiles with anxiety patterns",
                    affected_population={"anxiety_profiles": len(anxiety_profiles)},
                    confidence_score=0.75,
                    supporting_data={"profile_count": len(anxiety_profiles)},
                    recommendations=[
                        "Implement early anxiety screening",
                        "Develop anxiety-specific coping resources",
                        "Create anxiety support groups"
                    ],
                    created_at=datetime.now(timezone.utc),
                    severity=RiskLevel.MODERATE
                ))
            
            return opportunities
            
        except Exception as e:
            logger.error(f"Error identifying intervention opportunities: {e}")
            return []

    def _has_consent(self, user_id: str, consent_type: ConsentType) -> bool:
        """Check if user has given specific consent."""
        user_consents = self.user_consents.get(user_id, {})
        return user_consents.get(consent_type, False)

    def update_user_consent(self, user_id: str, consent_type: ConsentType, granted: bool):
        """Update user consent for data usage."""
        if user_id not in self.user_consents:
            self.user_consents[user_id] = {}
        
        self.user_consents[user_id][consent_type] = granted
        
        logger.info(f"Updated consent for {user_id}: {consent_type.value} = {granted}")
        monitoring.record_metric('consent_updated', 1.0, {
            'consent_type': consent_type.value,
            'granted': granted
        })

    @monitor_performance
    def create_research_dataset(self, title: str, description: str, 
                              profiles: List[AnonymizedProfile]) -> Optional[ResearchDataset]:
        """Create anonymized research dataset."""
        try:
            # Verify all profiles are properly anonymized
            if not self._verify_anonymization(profiles):
                logger.error("Anonymization verification failed")
                return None
            
            # Generate insights
            insights = self.generate_population_insights(profiles)
            
            dataset = ResearchDataset(
                dataset_id=str(uuid.uuid4()),
                title=title,
                description=description,
                data_points=len(profiles),
                anonymized_profiles=profiles,
                insights=insights,
                ethical_approval=True,  # Would be set based on review process
                consent_verified=True,
                created_at=datetime.now(timezone.utc),
                access_restrictions={
                    "requires_ethics_approval": True,
                    "academic_use_only": True,
                    "no_reidentification_attempts": True
                }
            )
            
            monitoring.record_metric('research_dataset_created', 1.0, {
                'data_points': len(profiles),
                'insights': len(insights)
            })
            
            return dataset
            
        except Exception as e:
            logger.error(f"Error creating research dataset: {e}")
            monitoring.record_error(e, 'dataset_creation')
            return None

    def _verify_anonymization(self, profiles: List[AnonymizedProfile]) -> bool:
        """Verify that profiles are properly anonymized."""
        for profile in profiles:
            # Check that no direct identifiers are present
            if any(field in str(profile) for field in ['email', 'phone', 'address']):
                return False
            
            # Check that age ranges are broad enough
            if profile.age_range and '-' not in profile.age_range:
                return False
        
        return True
