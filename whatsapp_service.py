"""
WhatsApp Business API Integration Service for AI Companion System.
Enables deployment as a WhatsApp bot with message handling, media support, and user authentication.
"""

import os
import json
import logging
import asyncio
import hashlib
import hmac
from datetime import datetime, timezone
from typing import Dict, List, Optional, Any, Union
from dataclasses import dataclass
import requests
from flask import Flask, request, jsonify
import threading
import time

from models import UserProfile, EmotionType, InteractionType
from conversation_service import ConversationService
from config import settings
from monitoring_service import monitoring, monitor_performance, AlertLevel

logger = logging.getLogger(__name__)

@dataclass
class WhatsAppMessage:
    """WhatsApp message structure."""
    message_id: str
    from_number: str
    to_number: str
    message_type: str  # text, image, audio, video, document
    content: str
    timestamp: datetime
    media_url: Optional[str] = None
    media_id: Optional[str] = None
    context: Optional[Dict[str, Any]] = None

@dataclass
class WhatsAppUser:
    """WhatsApp user profile."""
    phone_number: str
    name: Optional[str]
    profile_name: Optional[str]
    user_id: str  # Internal user ID mapped from phone number
    is_verified: bool = False
    consent_given: bool = False
    language: str = "en"

class WhatsAppService:
    """WhatsApp Business API integration service."""
    
    def __init__(self):
        """Initialize WhatsApp service with conversation integration."""
        self.conversation_service = ConversationService()
        
        # WhatsApp Business API configuration
        self.access_token = os.getenv('WHATSAPP_ACCESS_TOKEN')
        self.phone_number_id = os.getenv('WHATSAPP_PHONE_NUMBER_ID')
        self.verify_token = os.getenv('WHATSAPP_VERIFY_TOKEN')
        self.app_secret = os.getenv('WHATSAPP_APP_SECRET')
        
        # API endpoints
        self.base_url = f"https://graph.facebook.com/v18.0/{self.phone_number_id}"
        
        # User management
        self.active_users: Dict[str, WhatsAppUser] = {}
        self.user_sessions: Dict[str, Dict[str, Any]] = {}
        
        # Rate limiting
        self.rate_limits: Dict[str, List[float]] = {}
        self.max_messages_per_minute = 10
        
        # Flask app for webhook
        self.app = Flask(__name__)
        self._setup_webhook_routes()
        
        logger.info("WhatsApp Service initialized")

    def _setup_webhook_routes(self):
        """Setup Flask routes for WhatsApp webhook."""
        
        @self.app.route('/webhook', methods=['GET'])
        def verify_webhook():
            """Verify webhook for WhatsApp Business API."""
            mode = request.args.get('hub.mode')
            token = request.args.get('hub.verify_token')
            challenge = request.args.get('hub.challenge')
            
            if mode == 'subscribe' and token == self.verify_token:
                logger.info("Webhook verified successfully")
                return challenge
            else:
                logger.warning("Webhook verification failed")
                return 'Verification failed', 403

        @self.app.route('/webhook', methods=['POST'])
        def handle_webhook():
            """Handle incoming WhatsApp messages."""
            try:
                # Verify signature
                if not self._verify_signature(request.data, request.headers.get('X-Hub-Signature-256')):
                    logger.warning("Invalid webhook signature")
                    return 'Invalid signature', 403
                
                data = request.get_json()
                self._process_webhook_data(data)
                
                return 'OK', 200
                
            except Exception as e:
                logger.error(f"Error handling webhook: {e}")
                monitoring.record_error(e, 'whatsapp_webhook')
                return 'Error', 500

    def _verify_signature(self, payload: bytes, signature: str) -> bool:
        """Verify webhook signature for security."""
        if not signature or not self.app_secret:
            return False
            
        expected_signature = hmac.new(
            self.app_secret.encode(),
            payload,
            hashlib.sha256
        ).hexdigest()
        
        return hmac.compare_digest(f"sha256={expected_signature}", signature)

    @monitor_performance
    def _process_webhook_data(self, data: Dict[str, Any]):
        """Process incoming webhook data from WhatsApp."""
        try:
            entry = data.get('entry', [])
            for entry_item in entry:
                changes = entry_item.get('changes', [])
                for change in changes:
                    if change.get('field') == 'messages':
                        value = change.get('value', {})
                        
                        # Process incoming messages
                        messages = value.get('messages', [])
                        for message_data in messages:
                            self._handle_incoming_message(message_data, value)
                        
                        # Process message status updates
                        statuses = value.get('statuses', [])
                        for status in statuses:
                            self._handle_message_status(status)
                            
        except Exception as e:
            logger.error(f"Error processing webhook data: {e}")
            monitoring.record_error(e, 'webhook_processing')

    @monitor_performance
    def _handle_incoming_message(self, message_data: Dict[str, Any], value: Dict[str, Any]):
        """Handle incoming WhatsApp message."""
        try:
            # Extract message details
            from_number = message_data.get('from')
            message_id = message_data.get('id')
            timestamp = datetime.fromtimestamp(int(message_data.get('timestamp')), timezone.utc)
            
            # Get or create user
            user = self._get_or_create_user(from_number, value.get('contacts', []))
            
            # Check rate limiting
            if not self._check_rate_limit(from_number):
                self._send_rate_limit_message(from_number)
                return
            
            # Parse message content
            whatsapp_message = self._parse_message(message_data, from_number, timestamp)
            
            # Check if user has given consent for data processing
            if not user.consent_given and not self._is_consent_message(whatsapp_message.content):
                self._send_consent_request(from_number)
                return
            
            # Process consent
            if not user.consent_given and self._is_consent_message(whatsapp_message.content):
                if self._process_consent(whatsapp_message.content):
                    user.consent_given = True
                    self._send_welcome_message(from_number)
                    return
                else:
                    self._send_consent_declined_message(from_number)
                    return
            
            # Process message through conversation service
            asyncio.create_task(self._process_conversation_message(user, whatsapp_message))
            
            # Mark message as read
            self._mark_message_read(message_id)
            
        except Exception as e:
            logger.error(f"Error handling incoming message: {e}")
            monitoring.record_error(e, 'message_handling')

    def _get_or_create_user(self, phone_number: str, contacts: List[Dict]) -> WhatsAppUser:
        """Get existing user or create new one."""
        if phone_number in self.active_users:
            return self.active_users[phone_number]
        
        # Extract contact info
        contact_info = {}
        for contact in contacts:
            if contact.get('wa_id') == phone_number:
                contact_info = contact
                break
        
        # Create internal user ID
        user_id = f"whatsapp_{hashlib.md5(phone_number.encode()).hexdigest()[:12]}"
        
        user = WhatsAppUser(
            phone_number=phone_number,
            name=contact_info.get('profile', {}).get('name'),
            profile_name=contact_info.get('profile', {}).get('name'),
            user_id=user_id
        )
        
        self.active_users[phone_number] = user
        logger.info(f"Created new WhatsApp user: {user_id}")
        
        return user

    def _parse_message(self, message_data: Dict[str, Any], from_number: str, timestamp: datetime) -> WhatsAppMessage:
        """Parse WhatsApp message data."""
        message_type = message_data.get('type', 'text')
        content = ""
        media_url = None
        media_id = None
        
        if message_type == 'text':
            content = message_data.get('text', {}).get('body', '')
        elif message_type in ['image', 'audio', 'video', 'document']:
            media_data = message_data.get(message_type, {})
            media_id = media_data.get('id')
            content = media_data.get('caption', f"[{message_type.upper()}]")
            # Note: Media URL would be retrieved separately via Media API
        elif message_type == 'voice':
            media_data = message_data.get('audio', {})
            media_id = media_data.get('id')
            content = "[VOICE MESSAGE]"
        
        return WhatsAppMessage(
            message_id=message_data.get('id'),
            from_number=from_number,
            to_number=self.phone_number_id,
            message_type=message_type,
            content=content,
            timestamp=timestamp,
            media_url=media_url,
            media_id=media_id
        )

    async def _process_conversation_message(self, user: WhatsAppUser, message: WhatsAppMessage):
        """Process message through conversation service."""
        try:
            # Get or create conversation
            conversation_id = self._get_conversation_id(user.user_id)
            
            # Process through conversation service
            result = await asyncio.to_thread(
                self.conversation_service.process_message,
                user.user_id,
                message.content,
                conversation_id
            )
            
            # Send response back to WhatsApp
            if result and result.get('response'):
                await self._send_message(
                    user.phone_number,
                    result['response'],
                    result.get('emotion')
                )
            
            # Record metrics
            monitoring.record_metric('whatsapp_message_processed', 1.0, {
                'user_id': user.user_id,
                'message_type': message.message_type
            })
            
        except Exception as e:
            logger.error(f"Error processing conversation message: {e}")
            monitoring.record_error(e, 'conversation_processing')
            
            # Send error message to user
            await self._send_message(
                user.phone_number,
                "I'm sorry, I'm having trouble processing your message right now. Please try again in a moment."
            )

    def _get_conversation_id(self, user_id: str) -> str:
        """Get or create conversation ID for user."""
        if user_id not in self.user_sessions:
            self.user_sessions[user_id] = {
                'conversation_id': f"whatsapp_{user_id}_{int(time.time())}",
                'created_at': datetime.now(timezone.utc)
            }
        
        return self.user_sessions[user_id]['conversation_id']

    async def _send_message(self, to_number: str, text: str, emotion: Optional[EmotionType] = None):
        """Send message to WhatsApp user."""
        try:
            # Add emotion indicator if present
            if emotion and hasattr(emotion, 'value'):
                emotion_emoji = self._get_emotion_emoji(emotion)
                text = f"{text} {emotion_emoji}"
            
            payload = {
                "messaging_product": "whatsapp",
                "to": to_number,
                "type": "text",
                "text": {"body": text}
            }
            
            headers = {
                "Authorization": f"Bearer {self.access_token}",
                "Content-Type": "application/json"
            }
            
            response = requests.post(
                f"{self.base_url}/messages",
                headers=headers,
                json=payload
            )
            
            if response.status_code == 200:
                logger.info(f"Message sent successfully to {to_number}")
                monitoring.record_metric('whatsapp_message_sent', 1.0)
            else:
                logger.error(f"Failed to send message: {response.text}")
                monitoring.record_metric('whatsapp_message_failed', 1.0)
                
        except Exception as e:
            logger.error(f"Error sending WhatsApp message: {e}")
            monitoring.record_error(e, 'message_sending')

    def _get_emotion_emoji(self, emotion: EmotionType) -> str:
        """Get emoji representation for emotion."""
        emotion_emojis = {
            EmotionType.JOY: "😊",
            EmotionType.SADNESS: "😔",
            EmotionType.ANGER: "😠",
            EmotionType.FEAR: "😰",
            EmotionType.SURPRISE: "😲",
            EmotionType.DISGUST: "😤",
            EmotionType.LOVE: "❤️",
            EmotionType.EXCITEMENT: "🎉",
            EmotionType.CALM: "😌",
            EmotionType.ANXIETY: "😟",
            EmotionType.HOPE: "🌟",
            EmotionType.GRATITUDE: "🙏"
        }
        return emotion_emojis.get(emotion, "")

    def start_webhook_server(self, host: str = "0.0.0.0", port: int = 5000):
        """Start the webhook server."""
        logger.info(f"Starting WhatsApp webhook server on {host}:{port}")
        self.app.run(host=host, port=port, debug=False)

    def _check_rate_limit(self, phone_number: str) -> bool:
        """Check if user is within rate limits."""
        current_time = time.time()
        
        if phone_number not in self.rate_limits:
            self.rate_limits[phone_number] = []
        
        # Remove old timestamps
        self.rate_limits[phone_number] = [
            timestamp for timestamp in self.rate_limits[phone_number]
            if current_time - timestamp < 60  # 1 minute window
        ]
        
        # Check if under limit
        if len(self.rate_limits[phone_number]) < self.max_messages_per_minute:
            self.rate_limits[phone_number].append(current_time)
            return True
        
        return False

    def _is_consent_message(self, content: str) -> bool:
        """Check if message contains consent."""
        consent_keywords = ['yes', 'agree', 'consent', 'ok', 'sure', 'accept']
        return any(keyword in content.lower() for keyword in consent_keywords)

    def _process_consent(self, content: str) -> bool:
        """Process consent response."""
        return self._is_consent_message(content)

    async def _send_consent_request(self, phone_number: str):
        """Send consent request message."""
        message = """
🤖 Welcome to your AI Companion!

I'm here to provide emotional support and meaningful conversations. To offer you the best experience, I need to:

• Remember our conversations and your preferences
• Learn about your interests and emotional patterns
• Provide personalized support

Your data is completely private and secure. Do you consent to this? Reply 'yes' to continue.
        """
        await self._send_message(phone_number, message.strip())

    async def _send_welcome_message(self, phone_number: str):
        """Send welcome message after consent."""
        message = """
Thank you! 🙏 I'm excited to be your AI companion.

I'm here to:
• Listen and provide emotional support
• Remember what matters to you
• Have meaningful conversations
• Help you through difficult times

Feel free to share anything on your mind. How are you feeling today?
        """
        await self._send_message(phone_number, message.strip())

    async def _send_consent_declined_message(self, phone_number: str):
        """Send message when consent is declined."""
        message = "I understand. If you change your mind, just send 'yes' and we can start our conversation. Take care! 🌟"
        await self._send_message(phone_number, message)

    async def _send_rate_limit_message(self, phone_number: str):
        """Send rate limit message."""
        message = "You're sending messages quite quickly! Please wait a moment before sending another message. I'm here when you're ready. 😊"
        await self._send_message(phone_number, message)

    def _mark_message_read(self, message_id: str):
        """Mark message as read."""
        try:
            payload = {
                "messaging_product": "whatsapp",
                "status": "read",
                "message_id": message_id
            }
            
            headers = {
                "Authorization": f"Bearer {self.access_token}",
                "Content-Type": "application/json"
            }
            
            requests.post(
                f"{self.base_url}/messages",
                headers=headers,
                json=payload
            )
            
        except Exception as e:
            logger.error(f"Error marking message as read: {e}")

    def _handle_message_status(self, status: Dict[str, Any]):
        """Handle message status updates (delivered, read, etc.)."""
        message_id = status.get('id')
        status_type = status.get('status')
        
        logger.debug(f"Message {message_id} status: {status_type}")
        
        monitoring.record_metric('whatsapp_message_status', 1.0, {
            'status': status_type
        })
