"""
Gradio Web Interface for the AI Companion System.
Provides a modern, user-friendly interface for interacting with the AI companion.
"""

import gradio as gr
import json
from datetime import datetime
from typing import Dict, List, Any, Optional
import uuid

from conversation_service import ConversationService
from models import EmotionType, MemoryType
from config import settings

class AICompanionInterface:
    """Gradio interface for the AI Companion System."""
    
    def __init__(self):
        """Initialize the interface."""
        self.conversation_service = ConversationService()
        self.current_user_id = None
        self.current_conversation_id = None
        
        # Initialize the interface
        self.interface = self._create_interface()
    
    def _create_interface(self) -> gr.Blocks:
        """Create the Gradio interface."""
        with gr.Blocks(
            title="AI Companion - Your Personal AI Friend",
            theme=gr.themes.Soft(),
            css="""
            .chat-container {
                height: 500px;
                overflow-y: auto;
                border: 1px solid #e0e0e0;
                border-radius: 10px;
                padding: 20px;
                background: #fafafa;
            }
            .message {
                margin: 10px 0;
                padding: 15px;
                border-radius: 15px;
                max-width: 80%;
            }
            .user-message {
                background: #007bff;
                color: white;
                margin-left: auto;
            }
            .assistant-message {
                background: #f8f9fa;
                border: 1px solid #e9ecef;
            }
            .emotion-indicator {
                display: inline-block;
                padding: 5px 10px;
                border-radius: 15px;
                font-size: 12px;
                margin-left: 10px;
            }
            .stats-container {
                display: grid;
                grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
                gap: 15px;
                margin: 20px 0;
            }
            .stat-card {
                background: white;
                padding: 15px;
                border-radius: 10px;
                border: 1px solid #e0e0e0;
                text-align: center;
            }
            """
        ) as interface:
            
            gr.Markdown("""
            # 🤖 AI Companion - Your Personal AI Friend
            
            Welcome to your AI companion! This system learns and adapts to you over time, 
            remembering your preferences, emotions, and creating a personalized experience.
            
            **Features:**
            - 🧠 **Dual Memory Architecture**: Personal memories (private) + Universal knowledge (shared)
            - 💭 **Emotional Intelligence**: Understands and responds to your emotions
            - 🎯 **Personalized Learning**: Adapts to your communication style and interests
            - 🔒 **Privacy-First**: Your personal data stays isolated and secure
            """)
            
            with gr.Row():
                with gr.Column(scale=2):
                    # Main chat interface
                    with gr.Group():
                        gr.Markdown("### 💬 Conversation")
                        
                        # Chat display
                        chat_display = gr.HTML(
                            value="<div class='chat-container'><p style='text-align: center; color: #666;'>Start a conversation by typing a message below...</p></div>",
                            label="Chat History"
                        )
                        
                        # Message input
                        with gr.Row():
                            message_input = gr.Textbox(
                                placeholder="Type your message here...",
                                label="Your Message",
                                lines=2,
                                scale=4
                            )
                            send_button = gr.Button("Send", variant="primary", scale=1)
                        
                        # Quick actions
                        with gr.Row():
                            clear_button = gr.Button("Clear Chat", variant="secondary")
                            end_conversation_button = gr.Button("End Conversation", variant="secondary")
                
                with gr.Column(scale=1):
                    # User profile and insights
                    with gr.Group():
                        gr.Markdown("### 👤 User Profile")
                        
                        user_id_input = gr.Textbox(
                            placeholder="Enter your user ID (or leave empty for demo)",
                            label="User ID",
                            value="demo_user"
                        )
                        
                        user_name_input = gr.Textbox(
                            placeholder="Enter your name (optional)",
                            label="Name"
                        )
                        
                        start_session_button = gr.Button("Start Session", variant="primary")
                    
                    # Statistics and insights
                    with gr.Group():
                        gr.Markdown("### 📊 Insights")
                        
                        stats_display = gr.HTML(
                            value="<div class='stats-container'><div class='stat-card'><h4>No Data</h4><p>Start a conversation to see insights</p></div></div>",
                            label="User Statistics"
                        )
                        
                        refresh_stats_button = gr.Button("Refresh Insights", variant="secondary")

                    # Emotional insights
                    with gr.Group():
                        gr.Markdown("### 💭 Emotional Insights")

                        emotional_insights_display = gr.HTML(
                            value="<div class='stats-container'><div class='stat-card'><h4>💭 Emotional Insights</h4><p>Start chatting to see emotional insights</p></div></div>",
                            label="Emotional Insights"
                        )

                        refresh_emotional_button = gr.Button("Refresh Emotional Insights", variant="secondary")

                    # Memory exploration
                    with gr.Group():
                        gr.Markdown("### 🧠 Memory Explorer")
                        
                        memory_query = gr.Textbox(
                            placeholder="Search your memories...",
                            label="Memory Query"
                        )
                        
                        memory_results = gr.HTML(
                            value="<p style='color: #666;'>Search your personal memories here...</p>",
                            label="Memory Results"
                        )
                        
                        search_memories_button = gr.Button("Search Memories", variant="secondary")
            
            # Hidden state
            conversation_id_state = gr.State("")
            user_id_state = gr.State("")
            
            # Event handlers
            def start_session(user_id, user_name):
                """Start a new conversation session."""
                if not user_id:
                    user_id = f"user_{uuid.uuid4().hex[:8]}"
                
                conversation_id = self.conversation_service.start_conversation(user_id, user_name)
                
                welcome_message = f"""
                <div class='chat-container'>
                    <div class='message assistant-message'>
                        <strong>AI Companion:</strong> Hello{f" {user_name}" if user_name else ""}! 👋 
                        I'm your AI companion, and I'm here to chat, learn about you, and be a supportive friend. 
                        How are you feeling today?
                    </div>
                </div>
                """
                
                return (
                    welcome_message,
                    conversation_id,
                    user_id,
                    self._get_user_stats(user_id),
                    self._get_emotional_insights_html(user_id)
                )
            
            def send_message(message, conversation_id, user_id):
                """Send a message and get response."""
                if not message.strip():
                    return "", "", ""
                
                # Process message
                result = self.conversation_service.process_message(
                    conversation_id, message, user_id
                )
                
                if 'error' in result:
                    response_html = f"""
                    <div class='message assistant-message'>
                        <strong>AI Companion:</strong> {result['response']}
                        <span class='emotion-indicator' style='background: #ff6b6b; color: white;'>Error</span>
                    </div>
                    """
                else:
                    # Get emotion analysis
                    emotion = result.get('emotion_analysis', {}).get('emotion', 'neutral')
                    emotion_color = self._get_emotion_color(emotion)
                    
                    # Handle emotion display safely
                    emotion_display = emotion.value if hasattr(emotion, 'value') else str(emotion)

                    response_html = f"""
                    <div class='message assistant-message'>
                        <strong>AI Companion:</strong> {result['response']}
                        <span class='emotion-indicator' style='background: {emotion_color}; color: white;'>{emotion_display}</span>
                    </div>
                    """
                
                # Update chat display
                current_chat = self._get_chat_display(conversation_id)
                new_message_html = f"""
                <div class='message user-message'>
                    <strong>You:</strong> {message}
                </div>
                {response_html}
                """
                
                updated_chat = current_chat.replace("</div>", f"{new_message_html}</div>")
                
                return (
                    updated_chat,
                    "",
                    self._get_user_stats(user_id)
                )
            
            def clear_chat():
                """Clear the chat display."""
                return "<div class='chat-container'><p style='text-align: center; color: #666;'>Chat cleared. Start a new conversation...</p></div>"
            
            def end_conversation(conversation_id):
                """End the current conversation."""
                if conversation_id:
                    result = self.conversation_service.end_conversation(conversation_id)
                    
                    summary_html = f"""
                    <div class='chat-container'>
                        <div class='message assistant-message'>
                            <strong>Conversation Summary:</strong><br>
                            • Messages: {result.get('total_messages', 0)}<br>
                            • Topics: {', '.join(result.get('topics_discussed', []))}<br>
                            • Emotional Arc: {result.get('emotional_arc', {}).get('emotional_arc', 'unknown')}<br>
                            • Summary: {result.get('summary', 'No summary available')}
                        </div>
                    </div>
                    """
                    
                    return summary_html, ""
                return "", ""
            
            def search_memories(query, user_id):
                """Search user memories."""
                if not query.strip() or not user_id:
                    return "<p style='color: #666;'>Enter a query and user ID to search memories...</p>"
                
                try:
                    memories = self.conversation_service.memory_service.retrieve_relevant_memories(
                        user_id, query, MemoryType.PERSONAL, limit=5
                    )
                    
                    if not memories:
                        return "<p style='color: #666;'>No relevant memories found.</p>"
                    
                    memory_html = "<div style='max-height: 300px; overflow-y: auto;'>"
                    for memory in memories:
                        memory_html += f"""
                        <div style='background: white; padding: 10px; margin: 5px 0; border-radius: 5px; border-left: 3px solid #007bff;'>
                            <strong>Memory:</strong> {memory.content}<br>
                            <small style='color: #666;'>Type: {memory.interaction_type.value if hasattr(memory.interaction_type, 'value') else str(memory.interaction_type)} | Emotion: {memory.emotion.value if memory.emotion and hasattr(memory.emotion, 'value') else str(memory.emotion) if memory.emotion else 'neutral'} | Created: {memory.created_at.strftime('%Y-%m-%d %H:%M')}</small>
                        </div>
                        """
                    memory_html += "</div>"
                    
                    return memory_html
                    
                except Exception as e:
                    return f"<p style='color: #ff6b6b;'>Error searching memories: {str(e)}</p>"
            
            def refresh_stats(user_id):
                """Refresh user statistics."""
                return self._get_user_stats(user_id)

            def refresh_emotional_insights(user_id):
                """Refresh emotional insights."""
                return self._get_emotional_insights_html(user_id)
            
            # Connect event handlers
            start_session_button.click(
                start_session,
                inputs=[user_id_input, user_name_input],
                outputs=[chat_display, conversation_id_state, user_id_state, stats_display, emotional_insights_display]
            )
            
            send_button.click(
                send_message,
                inputs=[message_input, conversation_id_state, user_id_state],
                outputs=[chat_display, message_input, stats_display]
            )
            
            message_input.submit(
                send_message,
                inputs=[message_input, conversation_id_state, user_id_state],
                outputs=[chat_display, message_input, stats_display]
            )
            
            clear_button.click(
                clear_chat,
                outputs=[chat_display]
            )
            
            end_conversation_button.click(
                end_conversation,
                inputs=[conversation_id_state],
                outputs=[chat_display, conversation_id_state]
            )
            
            search_memories_button.click(
                search_memories,
                inputs=[memory_query, user_id_state],
                outputs=[memory_results]
            )
            
            refresh_stats_button.click(
                refresh_stats,
                inputs=[user_id_state],
                outputs=[stats_display]
            )

            refresh_emotional_button.click(
                refresh_emotional_insights,
                inputs=[user_id_state],
                outputs=[emotional_insights_display]
            )
        
        return interface
    
    def _get_chat_display(self, conversation_id: str) -> str:
        """Get current chat display HTML."""
        if not conversation_id:
            return "<div class='chat-container'><p style='text-align: center; color: #666;'>No active conversation...</p></div>"
        
        try:
            history = self.conversation_service.get_conversation_history(conversation_id)
            
            if not history:
                return "<div class='chat-container'><p style='text-align: center; color: #666;'>Start a conversation...</p></div>"
            
            chat_html = "<div class='chat-container'>"
            for msg in history:
                if msg.get('role') == 'user':
                    chat_html += f"""
                    <div class='message user-message'>
                        <strong>You:</strong> {msg.get('content', '')}
                    </div>
                    """
                else:
                    emotion = msg.get('emotion', 'neutral')
                    emotion_color = self._get_emotion_color(emotion)
                    emotion_display = emotion.value if hasattr(emotion, 'value') else str(emotion)
                    chat_html += f"""
                    <div class='message assistant-message'>
                        <strong>AI Companion:</strong> {msg.get('content', '')}
                        <span class='emotion-indicator' style='background: {emotion_color}; color: white;'>{emotion_display}</span>
                    </div>
                    """
            chat_html += "</div>"
            
            return chat_html
            
        except Exception:
            return "<div class='chat-container'><p style='text-align: center; color: #666;'>Error loading chat history...</p></div>"
    
    def _get_user_stats(self, user_id: str) -> str:
        """Get user statistics HTML."""
        if not user_id:
            return "<div class='stats-container'><div class='stat-card'><h4>No User</h4><p>Start a session to see stats</p></div></div>"
        
        try:
            insights = self.conversation_service.get_user_insights(user_id)
            
            if 'error' in insights:
                return f"<div class='stats-container'><div class='stat-card'><h4>Error</h4><p>{insights['error']}</p></div></div>"
            
            stats = insights.get('memory_statistics', {})
            profile = insights.get('user_profile', {})
            
            stats_html = f"""
            <div class='stats-container'>
                <div class='stat-card'>
                    <h4>📝 Interactions</h4>
                    <p>{profile.get('interaction_count', 0)}</p>
                </div>
                <div class='stat-card'>
                    <h4>🧠 Personal Memories</h4>
                    <p>{stats.get('personal_memories', 0)}</p>
                </div>
                <div class='stat-card'>
                    <h4>🌍 Universal Memories</h4>
                    <p>{stats.get('universal_memories', 0)}</p>
                </div>
                <div class='stat-card'>
                    <h4>🎯 Interests</h4>
                    <p>{len(profile.get('interests', []))}</p>
                </div>
            </div>
            """
            
            return stats_html
            
        except Exception as e:
            return f"<div class='stats-container'><div class='stat-card'><h4>Error</h4><p>{str(e)}</p></div></div>"
    
    def _get_emotion_color(self, emotion: EmotionType) -> str:
        """Get color for emotion indicator."""
        emotion_colors = {
            EmotionType.JOY: "#28a745",
            EmotionType.SADNESS: "#6c757d",
            EmotionType.ANGER: "#dc3545",
            EmotionType.FEAR: "#6f42c1",
            EmotionType.SURPRISE: "#fd7e14",
            EmotionType.DISGUST: "#20c997",
            EmotionType.NEUTRAL: "#6c757d",
            EmotionType.EXCITEMENT: "#ffc107",
            EmotionType.ANXIETY: "#e83e8c",
            EmotionType.CONTENTMENT: "#17a2b8"
        }
        return emotion_colors.get(emotion, "#6c757d")

    def _get_emotional_insights_html(self, user_id: str) -> str:
        """Generate HTML for emotional insights display."""
        try:
            insights = self.conversation_service.get_emotional_insights(user_id)
            empathy_rec = self.conversation_service.get_empathy_recommendations(user_id)

            if 'message' in insights:
                return "<div class='stats-container'><div class='stat-card'><h4>💭 Emotional Insights</h4><p>Start chatting to see emotional insights</p></div></div>"

            # Create emotional insights HTML
            insights_html = f"""
            <div class='stats-container'>
                <div class='stat-card'>
                    <h4>😊 Emotional Wellbeing</h4>
                    <p>Valence: {insights.get('average_valence', 0):.2f}</p>
                    <p>Stability: {insights.get('emotional_stability', 0):.2f}</p>
                </div>
                <div class='stat-card'>
                    <h4>📈 Emotional Trend</h4>
                    <p>{insights.get('recent_trend', 'stable').replace('_', ' ').title()}</p>
                </div>
                <div class='stat-card'>
                    <h4>🎭 Common Emotions</h4>
                    <p>{', '.join(list(insights.get('most_common_emotions', {}).keys())[:3])}</p>
                </div>
            """

            if 'message' not in empathy_rec:
                support_color = "#dc3545" if empathy_rec.get('support_needed', False) else "#28a745"
                insights_html += f"""
                <div class='stat-card'>
                    <h4>🤗 Support Status</h4>
                    <p style='color: {support_color}'>
                        {'Support Recommended' if empathy_rec.get('support_needed', False) else 'Doing Well'}
                    </p>
                </div>
                """

            insights_html += "</div>"
            return insights_html

        except Exception as e:
            return f"<div class='stats-container'><div class='stat-card'><h4>Error</h4><p>{str(e)}</p></div></div>"

    def launch(self, **kwargs):
        """Launch the Gradio interface."""
        return self.interface.launch(
            server_port=settings.gradio_port,
            share=False,
            **kwargs
        )

def main():
    """Main function to run the AI Companion interface."""
    print("🤖 Starting AI Companion System...")
    print(f"📡 Interface will be available at: http://localhost:{settings.gradio_port}")
    print("🔑 Make sure to set your GEMINI_API_KEY in the .env file")
    
    interface = AICompanionInterface()
    interface.launch()

if __name__ == "__main__":
    main() 