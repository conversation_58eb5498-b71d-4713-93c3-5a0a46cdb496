# AI Companion System - Complete Deployment Guide

This comprehensive guide covers deployment and configuration for the enhanced AI Companion System with WhatsApp integration, mental health data platform, and microservices architecture.

## 🚀 Quick Start

### Prerequisites

- Python 3.11 or higher
- Redis server
- PostgreSQL (recommended for production)
- Docker (for microservices deployment)
- Git

### 1. <PERSON><PERSON> and Install

```bash
git clone <repository-url>
cd ai-companion-system
pip install -r requirements.txt
```

### 2. Environment Configuration

Create a `.env` file in the root directory:

```env
# Required API Keys
GEMINI_API_KEY=AIzaSyCXTpUsu7Lw_UC64jttrOpy1ejnVHcOrHI

# WhatsApp Business API (for WhatsApp bot deployment)
WHATSAPP_ACCESS_TOKEN=your_whatsapp_access_token
WHATSAPP_PHONE_NUMBER_ID=your_phone_number_id
WHATSAPP_VERIFY_TOKEN=your_verify_token
WHATSAPP_APP_SECRET=your_app_secret

# Database Configuration
REDIS_URL=redis://localhost:6379
DATABASE_URL=sqlite:///./ai_companion.db

# System Configuration
LOG_LEVEL=INFO
DEBUG_MODE=true
SECRET_KEY=your-secret-key-change-in-production
ENVIRONMENT=development

# Memory Configuration
MEMORY_TTL=2592000  # 30 days
PERSONAL_MEMORY_SIZE=1000
UNIVERSAL_MEMORY_SIZE=10000

# Learning Configuration
LEARNING_RATE=0.1
ADAPTATION_THRESHOLD=5
EMOTION_WEIGHT=0.3

# Conversation Configuration
MAX_CONTEXT_LENGTH=2000
MAX_HISTORY_LENGTH=50
RESPONSE_TEMPERATURE=0.7
MAX_TOKENS=150

# Service Ports (for microservices)
GRADIO_PORT=7860
API_PORT=8000
MEMORY_SERVICE_PORT=8001
LEARNING_SERVICE_PORT=8002
```

### 3. Start Infrastructure Services

**Redis:**
```bash
# macOS (Homebrew)
brew install redis && brew services start redis

# Ubuntu/Debian
sudo apt install redis-server && sudo systemctl start redis-server

# Docker
docker run -d -p 6379:6379 redis:alpine
```

**PostgreSQL (Production):**
```bash
# Docker
docker run -d -p 5432:5432 -e POSTGRES_DB=ai_companion -e POSTGRES_USER=ai_companion -e POSTGRES_PASSWORD=password postgres:15-alpine
```

### 4. Run the System

**Single Application Mode:**
```bash
python main.py
```

**Microservices Mode (Docker):**
```bash
python deploy.py --platform docker
```

The system will be available at `http://localhost:7860`

## 🤖 WhatsApp Bot Deployment

### 1. WhatsApp Business API Setup

1. Create a Meta Developer Account at https://developers.facebook.com/
2. Create a WhatsApp Business App
3. Get your access token and phone number ID
4. Set up webhook URL: `https://your-domain.com/webhook`

### 2. Configure Webhook

```bash
# Set webhook URL
curl -X POST "https://graph.facebook.com/v18.0/YOUR_PHONE_NUMBER_ID/webhooks" \
  -H "Authorization: Bearer YOUR_ACCESS_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "webhooks": [
      {
        "url": "https://your-domain.com/webhook",
        "fields": ["messages"]
      }
    ]
  }'
```

### 3. Deploy WhatsApp Service

```bash
# Start WhatsApp service standalone
python whatsapp_service.py

# Or as part of microservices
python deploy.py --platform render --services whatsapp-service
```

## 🏥 Mental Health Data Platform

### 1. Enable Data Collection

Update your `.env`:
```env
# Mental Health Platform
ENABLE_MENTAL_HEALTH_PLATFORM=true
MIN_COHORT_SIZE=10
ANONYMIZATION_ENABLED=true
```

### 2. Configure Consent Management

The system automatically handles consent:
- Users must explicitly consent to data usage
- Different consent types for different purposes
- Full GDPR compliance

### 3. Access Research Dashboard

```bash
# Start mental health platform service
python mental_health_platform.py

# Access dashboard at http://localhost:8008
```

## 🐳 Microservices Deployment

### 1. Local Docker Deployment

```bash
# Generate docker-compose.yml
python deploy.py --platform docker

# Start all services
docker-compose up -d

# Check service health
docker-compose ps
```

### 2. Render.com Deployment (Recommended for Production)

```bash
# Deploy to Render
python deploy.py --platform render

# Deploy specific services
python deploy.py --platform render --services conversation-service whatsapp-service
```

### 3. Kubernetes Deployment

```bash
# Generate Kubernetes manifests
python deploy.py --platform kubernetes

# Apply to cluster
kubectl apply -f k8s/
```

## 🔧 Advanced Configuration

### Memory Architecture

```env
# Personal Memory (per user)
PERSONAL_MEMORY_SIZE=1000
PERSONAL_MEMORY_TTL=2592000  # 30 days

# Universal Memory (shared knowledge)
UNIVERSAL_MEMORY_SIZE=10000
UNIVERSAL_MEMORY_TTL=7776000  # 90 days

# Memory Processing
MEMORY_EXTRACTION_THRESHOLD=0.7
IMPORTANCE_SCORING_ENABLED=true
```

### Emotional Intelligence

```env
# Emotion Detection
EMOTION_DETECTION_MODEL=gemini
EMOTION_CONFIDENCE_THRESHOLD=0.6

# Empathy Configuration
EMPATHY_LEVEL=0.8
EMOTIONAL_MIRRORING=0.3
COMFORT_TECHNIQUES_ENABLED=true
```

### Learning and Adaptation

```env
# Learning Parameters
LEARNING_RATE=0.1
ADAPTATION_THRESHOLD=5
FEEDBACK_WEIGHT=0.4

# Content Curation
INTEREST_TRACKING_ENABLED=true
NEWS_INTEGRATION_ENABLED=true
PROACTIVE_SUGGESTIONS=true
```

## 📊 Monitoring and Analytics

### Health Checks

```bash
# Check system health
curl http://localhost:7860/health

# Check individual services
curl http://localhost:8001/health  # Conversation service
curl http://localhost:8002/health  # Memory service
curl http://localhost:8008/health  # Mental health platform
```

### Metrics and Logging

The system provides comprehensive monitoring:

- **Structured Logging**: JSON-formatted logs with correlation IDs
- **Performance Metrics**: Response times, error rates, resource usage
- **Business Metrics**: User engagement, emotional patterns, conversation quality
- **Health Monitoring**: Service availability, dependency health

### Alerts and Notifications

Configure alerts for:
- High error rates
- Service downtime
- Memory usage spikes
- Crisis detection (mental health)

## 🔒 Security and Privacy

### Data Protection

```env
# Encryption
DATA_ENCRYPTION_ENABLED=true
ENCRYPTION_KEY=your-encryption-key

# Privacy Settings
ANONYMIZATION_ENABLED=true
DATA_RETENTION_DAYS=90
GDPR_COMPLIANCE=true
```

### Access Control

```env
# Authentication
JWT_SECRET_KEY=your-jwt-secret
TOKEN_EXPIRY_HOURS=24

# Rate Limiting
RATE_LIMIT_REQUESTS=100
RATE_LIMIT_WINDOW=60
```

## 🧪 Testing

### Unit Tests

```bash
# Run all tests
pytest

# Run specific test categories
pytest tests/unit/
pytest tests/integration/
pytest tests/whatsapp/
```

### Load Testing

```bash
# Test conversation service
python tests/load_test_conversation.py

# Test WhatsApp webhook
python tests/load_test_whatsapp.py
```

## 🚨 Troubleshooting

### Common Issues

**1. Redis Connection Error**
```bash
# Check Redis status
redis-cli ping
# Should return: PONG

# Check connection
redis-cli -u $REDIS_URL ping
```

**2. WhatsApp Webhook Issues**
```bash
# Verify webhook
curl -X GET "https://your-domain.com/webhook?hub.mode=subscribe&hub.verify_token=YOUR_VERIFY_TOKEN&hub.challenge=test"
```

**3. Memory Service Issues**
```bash
# Check memory service logs
docker-compose logs memory-service

# Reset memory cache
redis-cli FLUSHDB
```

**4. Gemini API Issues**
```bash
# Test API key
curl -X POST "https://generativelanguage.googleapis.com/v1/models/gemini-pro:generateContent?key=$GEMINI_API_KEY" \
  -H "Content-Type: application/json" \
  -d '{"contents":[{"parts":[{"text":"Hello"}]}]}'
```

## 🌐 Production Deployment

### Render.com (Recommended)

1. **Prepare Repository:**
   ```bash
   # Create render.yaml
   python deploy.py --platform render --services all
   ```

2. **Deploy Services:**
   - Connect GitHub repository to Render
   - Configure environment variables
   - Deploy services in dependency order

3. **Configure Domains:**
   - Set up custom domains for each service
   - Configure SSL certificates
   - Set up load balancing

### Railway

```bash
# Deploy to Railway
railway login
railway init
railway up
```

### AWS/GCP/Azure

Use Kubernetes manifests:
```bash
# Generate manifests
python deploy.py --platform kubernetes

# Deploy to cloud
kubectl apply -f k8s/
```

This deployment guide provides everything needed to deploy and maintain your AI Companion System at scale. The system is now ready for production use with WhatsApp integration and mental health data platform capabilities.
